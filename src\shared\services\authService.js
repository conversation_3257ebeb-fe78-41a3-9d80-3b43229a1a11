import { apiClient } from '../../core/api/apiClient.js';

/**
 * Authentication Service
 */
class AuthService {
  constructor() {
    this.currentUser = null;
    this.loadUserFromStorage();
  }

  /**
   * Load user data from localStorage
   */
  loadUserFromStorage() {
    const userData = localStorage.getItem('user_data');
    if (userData) {
      try {
        this.currentUser = JSON.parse(userData);
      } catch (error) {
        console.error('Error parsing user data:', error);
        this.clearUserData();
      }
    }
  }

  /**
   * Save user data to localStorage
   * @param {Object} userData 
   */
  saveUserData(userData) {
    this.currentUser = userData;
    localStorage.setItem('user_data', JSON.stringify(userData));
  }

  /**
   * Clear user data from localStorage
   */
  clearUserData() {
    this.currentUser = null;
    localStorage.removeItem('user_data');
    localStorage.removeItem('auth_token');
    apiClient.setToken(null);
  }

  /**
   * Register new user
   * @param {Object} userData 
   * @returns {Promise<Object>}
   */
  async register(userData) {
    try {
      const response = await apiClient.post('/api/auth/register', userData);
      
      if (response.success) {
        // Save user data and token
        this.saveUserData(response.data.user);
        apiClient.setToken(response.data.token);
        
        return response;
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  /**
   * Login user
   * @param {Object} credentials 
   * @returns {Promise<Object>}
   */
  async login(credentials) {
    try {
      const response = await apiClient.post('/api/auth/login', credentials);
      
      if (response.success) {
        // Save user data and token
        this.saveUserData(response.data.user);
        apiClient.setToken(response.data.token);
        
        return response;
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Logout user
   * @returns {Promise<void>}
   */
  async logout() {
    try {
      // Call logout API if token exists
      if (apiClient.getToken()) {
        await apiClient.post('/api/auth/logout');
      }
    } catch (error) {
      console.error('Logout API error:', error);
      // Continue with local logout even if API call fails
    } finally {
      // Clear local data
      this.clearUserData();
    }
  }

  /**
   * Check if user is authenticated
   * @returns {boolean}
   */
  isAuthenticated() {
    return !!(this.currentUser && apiClient.getToken());
  }

  /**
   * Get current user
   * @returns {Object|null}
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * Get user token balance
   * @returns {number}
   */
  getTokenBalance() {
    return this.currentUser?.token_balance || 0;
  }

  /**
   * Check if user is active
   * @returns {boolean}
   */
  isUserActive() {
    return this.currentUser?.is_active || false;
  }

  /**
   * Get user type
   * @returns {string}
   */
  getUserType() {
    return this.currentUser?.user_type || 'user';
  }
}

// Create and export singleton instance
export const authService = new AuthService();
export default AuthService;
