/**
 * Simple SPA Router
 */
class Router {
  constructor() {
    this.routes = new Map();
    this.currentRoute = null;
    this.container = null;
    
    // Listen for browser navigation
    window.addEventListener('popstate', () => {
      this.handleRoute();
    });
  }

  /**
   * Set the container element where routes will be rendered
   * @param {HTMLElement|string} container 
   */
  setContainer(container) {
    if (typeof container === 'string') {
      this.container = document.querySelector(container);
    } else {
      this.container = container;
    }
  }

  /**
   * Register a route
   * @param {string} path 
   * @param {Function} handler 
   * @param {Object} options 
   */
  addRoute(path, handler, options = {}) {
    this.routes.set(path, {
      handler,
      requiresAuth: options.requiresAuth || false,
      ...options
    });
  }

  /**
   * Navigate to a route
   * @param {string} path 
   * @param {boolean} replace 
   */
  navigate(path, replace = false) {
    if (replace) {
      window.history.replaceState({}, '', path);
    } else {
      window.history.pushState({}, '', path);
    }
    this.handleRoute();
  }

  /**
   * Handle current route
   */
  async handleRoute() {
    const path = window.location.pathname;
    const route = this.routes.get(path);

    if (!route) {
      // Handle 404 - redirect to login or show 404 page
      this.navigate('/login', true);
      return;
    }

    // Check authentication if required
    if (route.requiresAuth && !this.isAuthenticated()) {
      this.navigate('/login', true);
      return;
    }

    // If user is authenticated and trying to access login/register, redirect to dashboard
    if ((path === '/login' || path === '/register') && this.isAuthenticated()) {
      this.navigate('/dashboard', true);
      return;
    }

    this.currentRoute = path;
    
    try {
      if (this.container) {
        // Clear container
        this.container.innerHTML = '';
        
        // Execute route handler
        await route.handler(this.container);
      }
    } catch (error) {
      console.error('Route handler error:', error);
      this.showError('An error occurred while loading the page.');
    }
  }

  /**
   * Check if user is authenticated
   * @returns {boolean}
   */
  isAuthenticated() {
    return !!localStorage.getItem('auth_token');
  }

  /**
   * Show error message
   * @param {string} message 
   */
  showError(message) {
    if (this.container) {
      this.container.innerHTML = `
        <div class="min-h-screen flex items-center justify-center bg-gray-50">
          <div class="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
            <div class="text-center">
              <div class="text-red-500 text-6xl mb-4">⚠️</div>
              <h1 class="text-2xl font-bold text-gray-900 mb-2">Error</h1>
              <p class="text-gray-600 mb-4">${message}</p>
              <button onclick="window.location.reload()" 
                      class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Reload Page
              </button>
            </div>
          </div>
        </div>
      `;
    }
  }

  /**
   * Start the router
   */
  start() {
    this.handleRoute();
  }

  /**
   * Get current route
   * @returns {string}
   */
  getCurrentRoute() {
    return this.currentRoute;
  }
}

// Create and export singleton instance
export const router = new Router();
export default Router;
