/**
 * Helper utilities
 */

/**
 * Create DOM element with attributes and content
 * @param {string} tag 
 * @param {Object} attributes 
 * @param {string|HTMLElement|Array} content 
 * @returns {HTMLElement}
 */
export function createElement(tag, attributes = {}, content = '') {
  const element = document.createElement(tag);
  
  // Set attributes
  for (const [key, value] of Object.entries(attributes)) {
    if (key === 'className') {
      element.className = value;
    } else if (key === 'innerHTML') {
      element.innerHTML = value;
    } else if (key.startsWith('on') && typeof value === 'function') {
      // Event listeners
      element.addEventListener(key.slice(2).toLowerCase(), value);
    } else {
      element.setAttribute(key, value);
    }
  }
  
  // Set content
  if (typeof content === 'string') {
    element.textContent = content;
  } else if (content instanceof HTMLElement) {
    element.appendChild(content);
  } else if (Array.isArray(content)) {
    content.forEach(child => {
      if (typeof child === 'string') {
        element.appendChild(document.createTextNode(child));
      } else if (child instanceof HTMLElement) {
        element.appendChild(child);
      }
    });
  }
  
  return element;
}

/**
 * Show loading spinner
 * @param {HTMLElement} container 
 */
export function showLoading(container) {
  container.innerHTML = `
    <div class="flex items-center justify-center min-h-64">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>
  `;
}

/**
 * Show error message
 * @param {HTMLElement} container 
 * @param {string} message 
 */
export function showError(container, message) {
  container.innerHTML = `
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <strong class="font-bold">Error!</strong>
      <span class="block sm:inline"> ${message}</span>
    </div>
  `;
}

/**
 * Show success message
 * @param {HTMLElement} container 
 * @param {string} message 
 */
export function showSuccess(container, message) {
  container.innerHTML = `
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      <strong class="font-bold">Success!</strong>
      <span class="block sm:inline"> ${message}</span>
    </div>
  `;
}

/**
 * Debounce function
 * @param {Function} func 
 * @param {number} wait 
 * @returns {Function}
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Format date
 * @param {string|Date} date 
 * @returns {string}
 */
export function formatDate(date) {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Sanitize HTML to prevent XSS
 * @param {string} str 
 * @returns {string}
 */
export function sanitizeHTML(str) {
  const temp = document.createElement('div');
  temp.textContent = str;
  return temp.innerHTML;
}
