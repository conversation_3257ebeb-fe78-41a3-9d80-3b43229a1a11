import { FormComponent } from '../../shared/components/FormComponent.js';
import { authService } from '../../shared/services/authService.js';
import { router } from '../../core/router/router.js';
import { validateForm } from '../../core/utils/validation.js';
import { showError, showSuccess } from '../../core/utils/helpers.js';

/**
 * Register page
 * @param {HTMLElement} container 
 */
export function renderRegisterPage(container) {
  // Create page container
  const pageContainer = document.createElement('div');
  pageContainer.className = 'min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8';

  // Create form container
  const formContainer = document.createElement('div');
  formContainer.className = 'max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md';
  
  // Create status container for messages
  const statusContainer = document.createElement('div');
  statusContainer.id = 'status-container';
  statusContainer.className = 'mb-4';
  
  formContainer.appendChild(statusContainer);

  // Create register form
  const registerForm = new FormComponent({
    title: 'Create your account',
    fields: [
      {
        name: 'email',
        label: 'Email address',
        type: 'email',
        placeholder: 'Enter your email',
        required: true
      },
      {
        name: 'password',
        label: 'Password',
        type: 'password',
        placeholder: 'Enter your password',
        required: true
      },
      {
        name: 'confirmPassword',
        label: 'Confirm Password',
        type: 'password',
        placeholder: 'Confirm your password',
        required: true
      }
    ],
    submitText: 'Create Account',
    links: [
      {
        text: 'Already have an account? Sign in',
        href: '/login',
        onClick: () => router.navigate('/login')
      }
    ],
    onSubmit: async (formData) => {
      try {
        // Validate form data
        const validation = validateForm(formData, {
          email: { required: true, email: true, maxLength: 255 },
          password: { required: true, password: true },
          confirmPassword: { required: true }
        });

        if (!validation.isValid) {
          // Show validation errors
          Object.entries(validation.errors).forEach(([field, errors]) => {
            registerForm.showFieldError(field, errors[0]);
          });
          return;
        }

        // Check if passwords match
        if (formData.password !== formData.confirmPassword) {
          registerForm.showFieldError('confirmPassword', 'Passwords do not match');
          return;
        }

        // Clear status container
        statusContainer.innerHTML = '';

        // Prepare registration data (exclude confirmPassword)
        const registrationData = {
          email: formData.email,
          password: formData.password
        };

        // Register user
        const response = await authService.register(registrationData);
        
        // Show success message
        showSuccess(statusContainer, 'Account created successfully! Redirecting...');
        
        // Redirect to dashboard after short delay
        setTimeout(() => {
          router.navigate('/dashboard');
        }, 1000);
      } catch (error) {
        // Show error message
        showError(statusContainer, error.message || 'Registration failed. Please try again.');
      }
    }
  });

  // Append form to container
  formContainer.appendChild(registerForm.create());
  pageContainer.appendChild(formContainer);
  container.appendChild(pageContainer);
}
