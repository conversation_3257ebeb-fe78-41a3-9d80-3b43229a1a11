<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, button { padding: 8px; width: 100%; box-sizing: border-box; }
        button { background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>API Test - PetaTalenta</h1>
        
        <h2>Register Test</h2>
        <form id="registerForm">
            <div class="form-group">
                <label>Email:</label>
                <input type="email" id="regEmail" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" id="regPassword" value="password123" required>
            </div>
            <button type="submit">Test Register</button>
        </form>
        
        <h2>Login Test</h2>
        <form id="loginForm">
            <div class="form-group">
                <label>Email:</label>
                <input type="email" id="loginEmail" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" id="loginPassword" value="password123" required>
            </div>
            <button type="submit">Test Login</button>
        </form>
        
        <h2>Logout Test</h2>
        <button id="logoutBtn">Test Logout</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'https://api.chhrone.web.id';
        let authToken = localStorage.getItem('test_token');
        
        function showResult(message, isError = false) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${isError ? 'error' : 'success'}`;
            result.style.display = 'block';
        }
        
        async function apiCall(endpoint, method = 'GET', data = null) {
            const config = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (authToken) {
                config.headers.Authorization = `Bearer ${authToken}`;
            }
            
            if (data) {
                config.body = JSON.stringify(data);
            }
            
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, config);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.message || `HTTP ${response.status}`);
                }
                
                return result;
            } catch (error) {
                throw error;
            }
        }
        
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            try {
                const result = await apiCall('/api/auth/register', 'POST', {
                    email: document.getElementById('regEmail').value,
                    password: document.getElementById('regPassword').value
                });
                
                if (result.data && result.data.token) {
                    authToken = result.data.token;
                    localStorage.setItem('test_token', authToken);
                }
                
                showResult(`Register Success: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                showResult(`Register Error: ${error.message}`, true);
            }
        });
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            try {
                const result = await apiCall('/api/auth/login', 'POST', {
                    email: document.getElementById('loginEmail').value,
                    password: document.getElementById('loginPassword').value
                });
                
                if (result.data && result.data.token) {
                    authToken = result.data.token;
                    localStorage.setItem('test_token', authToken);
                }
                
                showResult(`Login Success: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                showResult(`Login Error: ${error.message}`, true);
            }
        });
        
        document.getElementById('logoutBtn').addEventListener('click', async () => {
            try {
                const result = await apiCall('/api/auth/logout', 'POST');
                authToken = null;
                localStorage.removeItem('test_token');
                showResult(`Logout Success: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                showResult(`Logout Error: ${error.message}`, true);
            }
        });
    </script>
</body>
</html>
