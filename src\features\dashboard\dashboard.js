import { authService } from '../../shared/services/authService.js';
import { router } from '../../core/router/router.js';
import { createElement, formatDate } from '../../core/utils/helpers.js';

/**
 * Dashboard page
 * @param {HTMLElement} container 
 */
export function renderDashboardPage(container) {
  const user = authService.getCurrentUser();
  
  if (!user) {
    router.navigate('/login');
    return;
  }

  // Create page container
  const pageContainer = createElement('div', {
    className: 'min-h-screen bg-gray-50'
  });

  // Create header
  const header = createHeader(user);
  pageContainer.appendChild(header);

  // Create main content
  const mainContent = createElement('main', {
    className: 'max-w-7xl mx-auto py-6 sm:px-6 lg:px-8'
  });

  // Create dashboard content
  const dashboardContent = createElement('div', {
    className: 'px-4 py-6 sm:px-0'
  });

  // Welcome section
  const welcomeSection = createWelcomeSection(user);
  dashboardContent.appendChild(welcomeSection);

  // Stats section
  const statsSection = createStatsSection(user);
  dashboardContent.appendChild(statsSection);

  // User info section
  const userInfoSection = createUserInfoSection(user);
  dashboardContent.appendChild(userInfoSection);

  mainContent.appendChild(dashboardContent);
  pageContainer.appendChild(mainContent);
  container.appendChild(pageContainer);
}

/**
 * Create header component
 * @param {Object} user 
 * @returns {HTMLElement}
 */
function createHeader(user) {
  const header = createElement('header', {
    className: 'bg-white shadow'
  });

  const headerContent = createElement('div', {
    className: 'max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex justify-between items-center'
  });

  // Title
  const title = createElement('h1', {
    className: 'text-3xl font-bold text-gray-900'
  }, 'Dashboard');

  // User menu
  const userMenu = createElement('div', {
    className: 'flex items-center space-x-4'
  });

  const userInfo = createElement('span', {
    className: 'text-gray-700'
  }, `Welcome, ${user.email}`);

  const logoutButton = createElement('button', {
    className: 'bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded',
    onClick: handleLogout
  }, 'Logout');

  userMenu.appendChild(userInfo);
  userMenu.appendChild(logoutButton);

  headerContent.appendChild(title);
  headerContent.appendChild(userMenu);
  header.appendChild(headerContent);

  return header;
}

/**
 * Create welcome section
 * @param {Object} user 
 * @returns {HTMLElement}
 */
function createWelcomeSection(user) {
  return createElement('div', {
    className: 'bg-white overflow-hidden shadow rounded-lg mb-6'
  }, [
    createElement('div', {
      className: 'px-4 py-5 sm:p-6'
    }, [
      createElement('h2', {
        className: 'text-2xl font-bold text-gray-900 mb-2'
      }, `Welcome back, ${user.username || user.email}!`),
      createElement('p', {
        className: 'text-gray-600'
      }, 'Here\'s an overview of your account.')
    ])
  ]);
}

/**
 * Create stats section
 * @param {Object} user 
 * @returns {HTMLElement}
 */
function createStatsSection(user) {
  const statsContainer = createElement('div', {
    className: 'grid grid-cols-1 md:grid-cols-3 gap-6 mb-6'
  });

  // Token balance card
  const tokenCard = createElement('div', {
    className: 'bg-white overflow-hidden shadow rounded-lg'
  }, [
    createElement('div', {
      className: 'px-4 py-5 sm:p-6'
    }, [
      createElement('dt', {
        className: 'text-sm font-medium text-gray-500 truncate'
      }, 'Token Balance'),
      createElement('dd', {
        className: 'mt-1 text-3xl font-semibold text-gray-900'
      }, user.token_balance?.toString() || '0')
    ])
  ]);

  // Account status card
  const statusCard = createElement('div', {
    className: 'bg-white overflow-hidden shadow rounded-lg'
  }, [
    createElement('div', {
      className: 'px-4 py-5 sm:p-6'
    }, [
      createElement('dt', {
        className: 'text-sm font-medium text-gray-500 truncate'
      }, 'Account Status'),
      createElement('dd', {
        className: `mt-1 text-lg font-semibold ${user.is_active ? 'text-green-600' : 'text-red-600'}`
      }, user.is_active ? 'Active' : 'Inactive')
    ])
  ]);

  // User type card
  const typeCard = createElement('div', {
    className: 'bg-white overflow-hidden shadow rounded-lg'
  }, [
    createElement('div', {
      className: 'px-4 py-5 sm:p-6'
    }, [
      createElement('dt', {
        className: 'text-sm font-medium text-gray-500 truncate'
      }, 'User Type'),
      createElement('dd', {
        className: 'mt-1 text-lg font-semibold text-gray-900 capitalize'
      }, user.user_type || 'User')
    ])
  ]);

  statsContainer.appendChild(tokenCard);
  statsContainer.appendChild(statusCard);
  statsContainer.appendChild(typeCard);

  return statsContainer;
}

/**
 * Create user info section
 * @param {Object} user 
 * @returns {HTMLElement}
 */
function createUserInfoSection(user) {
  return createElement('div', {
    className: 'bg-white shadow overflow-hidden sm:rounded-lg'
  }, [
    createElement('div', {
      className: 'px-4 py-5 sm:px-6'
    }, [
      createElement('h3', {
        className: 'text-lg leading-6 font-medium text-gray-900'
      }, 'Account Information'),
      createElement('p', {
        className: 'mt-1 max-w-2xl text-sm text-gray-500'
      }, 'Your account details and information.')
    ]),
    createElement('div', {
      className: 'border-t border-gray-200'
    }, [
      createElement('dl', {}, [
        createInfoRow('User ID', user.id),
        createInfoRow('Email', user.email),
        createInfoRow('Username', user.username || 'Not set'),
        createInfoRow('Member Since', formatDate(user.created_at))
      ])
    ])
  ]);
}

/**
 * Create info row
 * @param {string} label 
 * @param {string} value 
 * @returns {HTMLElement}
 */
function createInfoRow(label, value) {
  return createElement('div', {
    className: 'bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6'
  }, [
    createElement('dt', {
      className: 'text-sm font-medium text-gray-500'
    }, label),
    createElement('dd', {
      className: 'mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2'
    }, value)
  ]);
}

/**
 * Handle logout
 */
async function handleLogout() {
  try {
    await authService.logout();
    router.navigate('/login');
  } catch (error) {
    console.error('Logout error:', error);
    // Force logout even if API call fails
    authService.clearUserData();
    router.navigate('/login');
  }
}
