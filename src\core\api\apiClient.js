/**
 * API Client for handling HTTP requests
 */
class ApiClient {
  constructor(baseURL = import.meta.env.VITE_API_BASE_URL || 'https://api.chhrone.web.id') {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('auth_token');
    this.timeout = import.meta.env.VITE_API_TIMEOUT || 10000;
  }

  /**
   * Set authentication token
   * @param {string} token 
   */
  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  /**
   * Get authentication token
   * @returns {string|null}
   */
  getToken() {
    return this.token || localStorage.getItem('auth_token');
  }

  /**
   * Make HTTP request
   * @param {string} endpoint 
   * @param {Object} options 
   * @returns {Promise<Object>}
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // Add authorization header if token exists
    const token = this.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  /**
   * GET request
   * @param {string} endpoint 
   * @param {Object} options 
   * @returns {Promise<Object>}
   */
  async get(endpoint, options = {}) {
    return this.request(endpoint, {
      method: 'GET',
      ...options,
    });
  }

  /**
   * POST request
   * @param {string} endpoint 
   * @param {Object} data 
   * @param {Object} options 
   * @returns {Promise<Object>}
   */
  async post(endpoint, data = {}, options = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options,
    });
  }

  /**
   * PUT request
   * @param {string} endpoint 
   * @param {Object} data 
   * @param {Object} options 
   * @returns {Promise<Object>}
   */
  async put(endpoint, data = {}, options = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options,
    });
  }

  /**
   * DELETE request
   * @param {string} endpoint 
   * @param {Object} options 
   * @returns {Promise<Object>}
   */
  async delete(endpoint, options = {}) {
    return this.request(endpoint, {
      method: 'DELETE',
      ...options,
    });
  }
}

// Create and export singleton instance
export const apiClient = new ApiClient();
export default ApiClient;
