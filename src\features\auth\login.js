import { FormComponent } from '../../shared/components/FormComponent.js';
import { authService } from '../../shared/services/authService.js';
import { router } from '../../core/router/router.js';
import { validateForm } from '../../core/utils/validation.js';
import { showError, showSuccess } from '../../core/utils/helpers.js';

/**
 * Login page
 * @param {HTMLElement} container 
 */
export function renderLoginPage(container) {
  // Create page container
  const pageContainer = document.createElement('div');
  pageContainer.className = 'min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8';

  // Create form container
  const formContainer = document.createElement('div');
  formContainer.className = 'max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md';
  
  // Create status container for messages
  const statusContainer = document.createElement('div');
  statusContainer.id = 'status-container';
  statusContainer.className = 'mb-4';
  
  formContainer.appendChild(statusContainer);

  // Create login form
  const loginForm = new FormComponent({
    title: 'Sign in to your account',
    fields: [
      {
        name: 'email',
        label: 'Email address',
        type: 'email',
        placeholder: 'Enter your email',
        required: true
      },
      {
        name: 'password',
        label: 'Password',
        type: 'password',
        placeholder: 'Enter your password',
        required: true
      }
    ],
    submitText: 'Sign in',
    links: [
      {
        text: 'Don\'t have an account? Register',
        href: '/register',
        onClick: () => router.navigate('/register')
      }
    ],
    onSubmit: async (formData) => {
      try {
        // Validate form data
        const validation = validateForm(formData, {
          email: { required: true, email: true },
          password: { required: true }
        });

        if (!validation.isValid) {
          // Show validation errors
          Object.entries(validation.errors).forEach(([field, errors]) => {
            loginForm.showFieldError(field, errors[0]);
          });
          return;
        }

        // Clear status container
        statusContainer.innerHTML = '';

        // Login user
        const response = await authService.login(formData);
        
        // Show success message
        showSuccess(statusContainer, 'Login successful! Redirecting...');
        
        // Redirect to dashboard after short delay
        setTimeout(() => {
          router.navigate('/dashboard');
        }, 1000);
      } catch (error) {
        // Show error message
        showError(statusContainer, error.message || 'Login failed. Please try again.');
      }
    }
  });

  // Append form to container
  formContainer.appendChild(loginForm.create());
  pageContainer.appendChild(formContainer);
  container.appendChild(pageContainer);
}
