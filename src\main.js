import './style.css';
import { router } from './core/router/router.js';
import { renderLoginPage } from './features/auth/login.js';
import { renderRegisterPage } from './features/auth/register.js';
import { renderDashboardPage } from './features/dashboard/dashboard.js';

/**
 * Initialize the application
 */
function initApp() {
  // Set router container
  router.setContainer('#app');

  // Register routes
  router.addRoute('/login', renderLoginPage);
  router.addRoute('/register', renderRegisterPage);
  router.addRoute('/dashboard', renderDashboardPage, { requiresAuth: true });
  router.addRoute('/', () => {
    // Redirect root to appropriate page
    if (router.isAuthenticated()) {
      router.navigate('/dashboard', true);
    } else {
      router.navigate('/login', true);
    }
  });

  // Start the router
  router.start();
}

// Initialize app when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  initApp();
}
