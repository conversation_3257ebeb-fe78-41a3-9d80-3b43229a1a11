[vite] Internal server error: [postcss] It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.
  Plugin: vite:css
  File: D:/(program-projects)/AI-Driven Talent Mapping Assessment/petatalenta-fe/src/style.css:undefined:NaN      
      at We (D:\(program-projects)\AI-Driven Talent Mapping Assessment\petatalenta-fe\node_modules\tailwindcss\dist\lib.js:35:2121)
      at LazyResult.runOnRoot (D:\(program-projects)\AI-Driven Talent Mapping Assessment\petatalenta-fe\node_modules\postcss\lib\lazy-result.js:361:16)
      at LazyResult.runAsync (D:\(program-projects)\AI-Driven Talent Mapping Assessment\petatalenta-fe\node_modules\postcss\lib\lazy-result.js:290:26)
      at LazyResult.async (D:\(program-projects)\AI-Driven Talent Mapping Assessment\petatalenta-fe\node_modules\postcss\lib\lazy-result.js:192:30)
      at LazyResult.then (D:\(program-projects)\AI-Driven Talent Mapping Assessment\petatalenta-fe\node_modules\postcss\lib\lazy-result.js:436:17) (x6)# API Configuration
VITE_API_BASE_URL=https://api.chhrone.web.id
VITE_API_TIMEOUT=10000

# Application Configuration
VITE_APP_NAME=PetaTalenta
VITE_APP_VERSION=1.0.0

# Development Configuration
VITE_DEV_PORT=5173
VITE_PREVIEW_PORT=3000
