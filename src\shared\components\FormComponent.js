import { createElement } from '../../core/utils/helpers.js';

/**
 * Reusable Form Component
 */
export class FormComponent {
  constructor(config) {
    this.config = config;
    this.formData = {};
    this.errors = {};
    this.isSubmitting = false;
    this.element = null;
  }

  /**
   * Create form element
   * @returns {HTMLElement}
   */
  create() {
    this.element = createElement('form', {
      className: 'space-y-6',
      onSubmit: (e) => this.handleSubmit(e)
    });

    // Add title if provided
    if (this.config.title) {
      const title = createElement('h2', {
        className: 'text-2xl font-bold text-gray-900 text-center mb-6'
      }, this.config.title);
      this.element.appendChild(title);
    }

    // Add fields
    this.config.fields.forEach(field => {
      const fieldElement = this.createField(field);
      this.element.appendChild(fieldElement);
    });

    // Add submit button
    const submitButton = this.createSubmitButton();
    this.element.appendChild(submitButton);

    // Add additional links if provided
    if (this.config.links) {
      const linksContainer = createElement('div', {
        className: 'text-center space-y-2'
      });

      this.config.links.forEach(link => {
        const linkElement = createElement('div', {}, [
          createElement('a', {
            href: link.href,
            className: 'text-blue-600 hover:text-blue-500 text-sm',
            onClick: (e) => {
              e.preventDefault();
              if (link.onClick) link.onClick();
            }
          }, link.text)
        ]);
        linksContainer.appendChild(linkElement);
      });

      this.element.appendChild(linksContainer);
    }

    return this.element;
  }

  /**
   * Create form field
   * @param {Object} field 
   * @returns {HTMLElement}
   */
  createField(field) {
    const fieldContainer = createElement('div', {
      className: 'space-y-1'
    });

    // Label
    const label = createElement('label', {
      className: 'block text-sm font-medium text-gray-700',
      for: field.name
    }, field.label);
    fieldContainer.appendChild(label);

    // Input
    const input = createElement('input', {
      type: field.type || 'text',
      id: field.name,
      name: field.name,
      placeholder: field.placeholder || '',
      className: 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
      required: field.required || false,
      onInput: (e) => this.handleInputChange(field.name, e.target.value)
    });
    fieldContainer.appendChild(input);

    // Error message container
    const errorContainer = createElement('div', {
      id: `${field.name}-error`,
      className: 'text-red-600 text-sm hidden'
    });
    fieldContainer.appendChild(errorContainer);

    return fieldContainer;
  }

  /**
   * Create submit button
   * @returns {HTMLElement}
   */
  createSubmitButton() {
    return createElement('button', {
      type: 'submit',
      className: 'w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed',
      id: 'submit-button'
    }, this.config.submitText || 'Submit');
  }

  /**
   * Handle input change
   * @param {string} name 
   * @param {string} value 
   */
  handleInputChange(name, value) {
    this.formData[name] = value;
    this.clearFieldError(name);
  }

  /**
   * Handle form submit
   * @param {Event} e 
   */
  async handleSubmit(e) {
    e.preventDefault();
    
    if (this.isSubmitting) return;

    this.clearAllErrors();
    this.setSubmitting(true);

    try {
      if (this.config.onSubmit) {
        await this.config.onSubmit(this.formData);
      }
    } catch (error) {
      this.handleError(error);
    } finally {
      this.setSubmitting(false);
    }
  }

  /**
   * Set submitting state
   * @param {boolean} submitting 
   */
  setSubmitting(submitting) {
    this.isSubmitting = submitting;
    const submitButton = this.element.querySelector('#submit-button');
    if (submitButton) {
      submitButton.disabled = submitting;
      submitButton.textContent = submitting ? 'Loading...' : (this.config.submitText || 'Submit');
    }
  }

  /**
   * Show field error
   * @param {string} fieldName 
   * @param {string} message 
   */
  showFieldError(fieldName, message) {
    const errorContainer = this.element.querySelector(`#${fieldName}-error`);
    const input = this.element.querySelector(`[name="${fieldName}"]`);
    
    if (errorContainer) {
      errorContainer.textContent = message;
      errorContainer.classList.remove('hidden');
    }
    
    if (input) {
      input.classList.add('border-red-500');
    }
  }

  /**
   * Clear field error
   * @param {string} fieldName 
   */
  clearFieldError(fieldName) {
    const errorContainer = this.element.querySelector(`#${fieldName}-error`);
    const input = this.element.querySelector(`[name="${fieldName}"]`);
    
    if (errorContainer) {
      errorContainer.classList.add('hidden');
    }
    
    if (input) {
      input.classList.remove('border-red-500');
    }
  }

  /**
   * Clear all errors
   */
  clearAllErrors() {
    this.config.fields.forEach(field => {
      this.clearFieldError(field.name);
    });
  }

  /**
   * Handle error
   * @param {Error} error 
   */
  handleError(error) {
    if (error.message.includes('validation') || error.message.includes('required')) {
      // Handle validation errors
      console.error('Validation error:', error);
    } else {
      // Show general error
      alert(error.message || 'An error occurred. Please try again.');
    }
  }

  /**
   * Get form data
   * @returns {Object}
   */
  getFormData() {
    return { ...this.formData };
  }

  /**
   * Reset form
   */
  reset() {
    this.formData = {};
    this.errors = {};
    if (this.element) {
      this.element.reset();
      this.clearAllErrors();
    }
  }
}
