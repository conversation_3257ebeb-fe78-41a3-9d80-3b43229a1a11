/**
 * Validation utilities
 */

/**
 * Validate email format
 * @param {string} email 
 * @returns {boolean}
 */
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 255;
}

/**
 * Validate password strength
 * @param {string} password 
 * @returns {Object}
 */
export function validatePassword(password) {
  const result = {
    isValid: true,
    errors: []
  };

  if (!password || password.length < 8) {
    result.isValid = false;
    result.errors.push('Password must be at least 8 characters long');
  }

  if (!/[a-zA-Z]/.test(password)) {
    result.isValid = false;
    result.errors.push('Password must contain at least one letter');
  }

  if (!/\d/.test(password)) {
    result.isValid = false;
    result.errors.push('Password must contain at least one number');
  }

  return result;
}

/**
 * Validate form data
 * @param {Object} data 
 * @param {Object} rules 
 * @returns {Object}
 */
export function validateForm(data, rules) {
  const errors = {};
  let isValid = true;

  for (const [field, fieldRules] of Object.entries(rules)) {
    const value = data[field];
    const fieldErrors = [];

    // Required validation
    if (fieldRules.required && (!value || value.trim() === '')) {
      fieldErrors.push(`${field} is required`);
    }

    // Email validation
    if (fieldRules.email && value && !isValidEmail(value)) {
      fieldErrors.push(`${field} must be a valid email address`);
    }

    // Password validation
    if (fieldRules.password && value) {
      const passwordValidation = validatePassword(value);
      if (!passwordValidation.isValid) {
        fieldErrors.push(...passwordValidation.errors);
      }
    }

    // Min length validation
    if (fieldRules.minLength && value && value.length < fieldRules.minLength) {
      fieldErrors.push(`${field} must be at least ${fieldRules.minLength} characters long`);
    }

    // Max length validation
    if (fieldRules.maxLength && value && value.length > fieldRules.maxLength) {
      fieldErrors.push(`${field} must not exceed ${fieldRules.maxLength} characters`);
    }

    if (fieldErrors.length > 0) {
      errors[field] = fieldErrors;
      isValid = false;
    }
  }

  return { isValid, errors };
}
