# PetaTalenta Frontend

Aplikasi frontend modular untuk platform talent mapping menggunakan Vanilla JavaScript, Vite, dan <PERSON>wind CSS.

## 🚀 Fitur

- **Modular Architecture**: Struktur folder yang terorganisir berdasarkan fitur
- **Authentication**: Login dan register dengan validasi
- **Dashboard**: Halaman dashboard dengan informasi user
- **Responsive Design**: Menggunakan Tailwind CSS
- **SPA Router**: Router sederhana untuk Single Page Application
- **API Integration**: Integrasi dengan API backend

## 📁 Struktur Proyek

```
src/
├── core/                   # Core functionality
│   ├── api/               # API client
│   ├── router/            # SPA router
│   └── utils/             # Utility functions
├── features/              # Feature modules
│   ├── auth/              # Authentication (login, register)
│   └── dashboard/         # Dashboard
├── shared/                # Shared components and services
│   ├── components/        # Reusable components
│   └── services/          # Shared services
├── main.js               # Application entry point
└── style.css             # Global styles
```

## 🛠️ Teknologi

- **Vanilla JavaScript**: Tanpa framework frontend
- **Vite**: Build tool dan development server
- **Tailwind CSS**: Utility-first CSS framework
- **Fetch API**: HTTP client untuk API calls

## 📋 Prerequisites

- Node.js (v14 atau lebih baru)
- npm atau yarn

## 🚀 Instalasi dan Menjalankan

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Jalankan development server**:
   ```bash
   npm run dev
   ```

3. **Build untuk production**:
   ```bash
   npm run build
   ```

4. **Preview production build**:
   ```bash
   npm run preview
   ```

## 🔧 Konfigurasi API

API base URL dikonfigurasi di `src/core/api/apiClient.js`:

```javascript
const baseURL = 'https://api.chhrone.web.id'
```

## 📚 API Endpoints

### Authentication

- **POST** `/api/auth/register` - Register user baru
- **POST** `/api/auth/login` - Login user
- **POST** `/api/auth/logout` - Logout user

## 🎯 Fitur Utama

### 1. Authentication System
- Form login dan register dengan validasi
- Token-based authentication
- Auto-redirect berdasarkan status login

### 2. Dashboard
- Informasi user (email, token balance, status)
- User statistics
- Logout functionality

### 3. Modular Components
- FormComponent: Komponen form yang reusable
- Router: SPA routing system
- API Client: HTTP client dengan authentication

## 🔒 Security Features

- Input validation dan sanitization
- XSS protection
- Token-based authentication
- Secure localStorage handling

## 📱 Responsive Design

Aplikasi menggunakan Tailwind CSS dengan breakpoints:
- Mobile-first approach
- Responsive grid system
- Adaptive components

## 🧪 Development

### Menambah Fitur Baru

1. Buat folder baru di `src/features/`
2. Implementasikan komponen dan logic
3. Daftarkan route di `src/main.js`
4. Update dokumentasi

### Struktur Component

```javascript
// Example component structure
export function renderComponentPage(container) {
  // Create elements
  // Add event listeners
  // Append to container
}
```

## 🤝 Contributing

1. Fork repository
2. Buat feature branch
3. Commit changes
4. Push ke branch
5. Buat Pull Request

## 📄 License

MIT License - lihat file LICENSE untuk detail.

## 🆘 Support

Jika ada pertanyaan atau masalah, silakan buat issue di repository ini.
